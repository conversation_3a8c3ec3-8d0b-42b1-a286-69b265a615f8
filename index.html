<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo List - TypeScript 应用</title>
    <link rel="stylesheet" href="dist/css/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">我的待办事项</h1>
            <p class="subtitle">使用 TypeScript 构建的简洁 Todo List</p>
        </header>

        <main class="main">
            <!-- 添加新任务区域 -->
            <section class="add-todo-section">
                <div class="input-group">
                    <input 
                        type="text" 
                        id="todoInput" 
                        class="todo-input" 
                        placeholder="输入新的待办事项..."
                        maxlength="200"
                    >
                    <button id="addBtn" class="add-btn">
                        <span class="btn-text">添加</span>
                        <span class="btn-icon">+</span>
                    </button>
                </div>
            </section>

            <!-- 筛选按钮区域 -->
            <section class="filter-section">
                <div class="filter-buttons">
                    <button id="filterAll" class="filter-btn active" data-filter="all">
                        全部 <span class="count" id="countAll">0</span>
                    </button>
                    <button id="filterActive" class="filter-btn" data-filter="active">
                        进行中 <span class="count" id="countActive">0</span>
                    </button>
                    <button id="filterCompleted" class="filter-btn" data-filter="completed">
                        已完成 <span class="count" id="countCompleted">0</span>
                    </button>
                </div>
            </section>

            <!-- 任务列表区域 -->
            <section class="todo-list-section">
                <ul id="todoList" class="todo-list">
                    <!-- 动态生成的任务项将在这里显示 -->
                </ul>
                
                <!-- 空状态提示 -->
                <div id="emptyState" class="empty-state">
                    <div class="empty-icon">📝</div>
                    <h3 class="empty-title">暂无待办事项</h3>
                    <p class="empty-description">添加您的第一个任务开始使用吧！</p>
                </div>
            </section>

            <!-- 批量操作区域 -->
            <section class="bulk-actions" id="bulkActions" style="display: none;">
                <button id="clearCompleted" class="bulk-btn clear-btn">
                    清除已完成
                </button>
                <button id="markAllComplete" class="bulk-btn mark-all-btn">
                    全部标记为完成
                </button>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2024 TypeScript Todo List</p>
        </footer>
    </div>

    <!-- 编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑任务</h3>
                <button id="closeModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <input type="text" id="editInput" class="edit-input" placeholder="编辑任务内容...">
            </div>
            <div class="modal-footer">
                <button id="cancelEdit" class="modal-btn cancel-btn">取消</button>
                <button id="saveEdit" class="modal-btn save-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 引入编译后的 JavaScript 文件 -->
    <script src="dist/js/main.js"></script>
</body>
</html>
