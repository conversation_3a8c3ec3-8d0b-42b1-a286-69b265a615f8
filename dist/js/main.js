"use strict";
class TodoApp {
    constructor() {
        this.todos = [];
        this.currentFilter = 'all';
        this.editingTodoId = null;
        this.initializeElements();
        this.bindEvents();
        this.loadTodos();
        this.render();
    }
    initializeElements() {
        this.todoInput = document.getElementById('todoInput');
        this.addBtn = document.getElementById('addBtn');
        this.todoList = document.getElementById('todoList');
        this.emptyState = document.getElementById('emptyState');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.bulkActions = document.getElementById('bulkActions');
        this.clearCompletedBtn = document.getElementById('clearCompleted');
        this.markAllCompleteBtn = document.getElementById('markAllComplete');
        this.editModal = document.getElementById('editModal');
        this.editInput = document.getElementById('editInput');
        this.saveEditBtn = document.getElementById('saveEdit');
        this.cancelEditBtn = document.getElementById('cancelEdit');
        this.closeModalBtn = document.getElementById('closeModal');
        this.countElements = {
            all: document.getElementById('countAll'),
            active: document.getElementById('countActive'),
            completed: document.getElementById('countCompleted')
        };
    }
    bindEvents() {
        this.addBtn.addEventListener('click', () => this.addTodo());
        this.todoInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTodo();
            }
        });
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.getAttribute('data-filter');
                this.setFilter(filter);
            });
        });
        this.clearCompletedBtn.addEventListener('click', () => this.clearCompleted());
        this.markAllCompleteBtn.addEventListener('click', () => this.markAllComplete());
        this.saveEditBtn.addEventListener('click', () => this.saveEdit());
        this.cancelEditBtn.addEventListener('click', () => this.closeEditModal());
        this.closeModalBtn.addEventListener('click', () => this.closeEditModal());
        this.editInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.saveEdit();
            }
            else if (e.key === 'Escape') {
                this.closeEditModal();
            }
        });
        this.editModal.addEventListener('click', (e) => {
            if (e.target === this.editModal) {
                this.closeEditModal();
            }
        });
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    addTodo() {
        const text = this.todoInput.value.trim();
        if (!text) {
            this.todoInput.focus();
            return;
        }
        const newTodo = {
            id: this.generateId(),
            text: text,
            completed: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.todos.unshift(newTodo);
        this.todoInput.value = '';
        this.saveTodos();
        this.render();
        this.todoInput.focus();
    }
    toggleTodo(id) {
        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            todo.completed = !todo.completed;
            todo.updatedAt = new Date();
            this.saveTodos();
            this.render();
        }
    }
    deleteTodo(id) {
        if (confirm('确定要删除这个任务吗？')) {
            this.todos = this.todos.filter(t => t.id !== id);
            this.saveTodos();
            this.render();
        }
    }
    startEdit(id) {
        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            this.editingTodoId = id;
            this.editInput.value = todo.text;
            this.editModal.classList.add('show');
            this.editInput.focus();
            this.editInput.select();
        }
    }
    saveEdit() {
        if (!this.editingTodoId)
            return;
        const newText = this.editInput.value.trim();
        if (!newText) {
            alert('任务内容不能为空！');
            this.editInput.focus();
            return;
        }
        const todo = this.todos.find(t => t.id === this.editingTodoId);
        if (todo) {
            todo.text = newText;
            todo.updatedAt = new Date();
            this.saveTodos();
            this.render();
        }
        this.closeEditModal();
    }
    closeEditModal() {
        this.editModal.classList.remove('show');
        this.editingTodoId = null;
        this.editInput.value = '';
    }
    setFilter(filter) {
        this.currentFilter = filter;
        this.filterButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-filter') === filter) {
                btn.classList.add('active');
            }
        });
        this.render();
    }
    getFilteredTodos() {
        switch (this.currentFilter) {
            case 'active':
                return this.todos.filter(todo => !todo.completed);
            case 'completed':
                return this.todos.filter(todo => todo.completed);
            default:
                return this.todos;
        }
    }
    clearCompleted() {
        const completedCount = this.todos.filter(t => t.completed).length;
        if (completedCount === 0) {
            alert('没有已完成的任务需要清除！');
            return;
        }
        if (confirm(`确定要清除 ${completedCount} 个已完成的任务吗？`)) {
            this.todos = this.todos.filter(t => !t.completed);
            this.saveTodos();
            this.render();
        }
    }
    markAllComplete() {
        const activeCount = this.todos.filter(t => !t.completed).length;
        if (activeCount === 0) {
            alert('所有任务都已完成！');
            return;
        }
        if (confirm(`确定要将 ${activeCount} 个任务标记为完成吗？`)) {
            this.todos.forEach(todo => {
                if (!todo.completed) {
                    todo.completed = true;
                    todo.updatedAt = new Date();
                }
            });
            this.saveTodos();
            this.render();
        }
    }
    updateCounts() {
        const allCount = this.todos.length;
        const activeCount = this.todos.filter(t => !t.completed).length;
        const completedCount = this.todos.filter(t => t.completed).length;
        this.countElements.all.textContent = allCount.toString();
        this.countElements.active.textContent = activeCount.toString();
        this.countElements.completed.textContent = completedCount.toString();
        if (allCount > 0) {
            this.bulkActions.style.display = 'flex';
        }
        else {
            this.bulkActions.style.display = 'none';
        }
    }
    createTodoElement(todo) {
        const li = document.createElement('li');
        li.className = `todo-item ${todo.completed ? 'completed' : ''}`;
        li.setAttribute('data-id', todo.id);
        li.innerHTML = `
            <div class="todo-checkbox ${todo.completed ? 'checked' : ''}" data-action="toggle"></div>
            <span class="todo-text ${todo.completed ? 'completed' : ''}">${this.escapeHtml(todo.text)}</span>
            <div class="todo-actions">
                <button class="action-btn edit-btn" data-action="edit">编辑</button>
                <button class="action-btn delete-btn" data-action="delete">删除</button>
            </div>
        `;
        li.addEventListener('click', (e) => {
            const target = e.target;
            const action = target.getAttribute('data-action');
            switch (action) {
                case 'toggle':
                    this.toggleTodo(todo.id);
                    break;
                case 'edit':
                    this.startEdit(todo.id);
                    break;
                case 'delete':
                    this.deleteTodo(todo.id);
                    break;
            }
        });
        return li;
    }
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    render() {
        const filteredTodos = this.getFilteredTodos();
        this.todoList.innerHTML = '';
        if (filteredTodos.length === 0) {
            this.emptyState.style.display = 'block';
            this.todoList.style.display = 'none';
        }
        else {
            this.emptyState.style.display = 'none';
            this.todoList.style.display = 'block';
            filteredTodos.forEach(todo => {
                const todoElement = this.createTodoElement(todo);
                this.todoList.appendChild(todoElement);
            });
        }
        this.updateCounts();
    }
    saveTodos() {
        try {
            const todosJson = JSON.stringify(this.todos.map(todo => (Object.assign(Object.assign({}, todo), { createdAt: todo.createdAt.toISOString(), updatedAt: todo.updatedAt.toISOString() }))));
            localStorage.setItem('todos', todosJson);
        }
        catch (error) {
            console.error('保存数据失败:', error);
            alert('保存数据失败，请检查浏览器存储空间！');
        }
    }
    loadTodos() {
        try {
            const todosJson = localStorage.getItem('todos');
            if (todosJson) {
                const todosData = JSON.parse(todosJson);
                this.todos = todosData.map((todo) => (Object.assign(Object.assign({}, todo), { createdAt: new Date(todo.createdAt), updatedAt: new Date(todo.updatedAt) })));
            }
        }
        catch (error) {
            console.error('加载数据失败:', error);
            this.todos = [];
            alert('加载数据失败，将使用空列表开始！');
        }
    }
}
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
    console.log('Todo List 应用程序已启动！');
});
//# sourceMappingURL=main.js.map