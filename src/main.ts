/**
 * Todo List Application - TypeScript Implementation
 * 功能完整的待办事项管理应用程序
 */

// ==================== 数据模型定义 ====================

/**
 * Todo 项目的接口定义
 */
interface Todo {
    id: string;           // 唯一标识符
    text: string;         // 任务内容
    completed: boolean;   // 完成状态
    createdAt: Date;      // 创建时间
    updatedAt: Date;      // 更新时间
}

/**
 * 筛选类型定义
 */
type FilterType = 'all' | 'active' | 'completed';

// ==================== 应用程序类 ====================

/**
 * TodoApp 主应用程序类
 * 负责管理所有的 Todo 项目和用户交互
 */
class TodoApp {
    private todos: Todo[] = [];
    private currentFilter: FilterType = 'all';
    private editingTodoId: string | null = null;
    
    // DOM 元素引用
    private todoInput!: HTMLInputElement;
    private addBtn!: HTMLButtonElement;
    private todoList!: HTMLUListElement;
    private emptyState!: HTMLElement;
    private filterButtons!: NodeListOf<HTMLButtonElement>;
    private bulkActions!: HTMLElement;
    private clearCompletedBtn!: HTMLButtonElement;
    private markAllCompleteBtn!: HTMLButtonElement;
    private editModal!: HTMLElement;
    private editInput!: HTMLInputElement;
    private saveEditBtn!: HTMLButtonElement;
    private cancelEditBtn!: HTMLButtonElement;
    private closeModalBtn!: HTMLButtonElement;
    private countElements!: {
        all: HTMLElement;
        active: HTMLElement;
        completed: HTMLElement;
    };

    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.loadTodos();
        this.render();
    }

    /**
     * 初始化 DOM 元素引用
     */
    private initializeElements(): void {
        this.todoInput = document.getElementById('todoInput') as HTMLInputElement;
        this.addBtn = document.getElementById('addBtn') as HTMLButtonElement;
        this.todoList = document.getElementById('todoList') as HTMLUListElement;
        this.emptyState = document.getElementById('emptyState') as HTMLElement;
        this.filterButtons = document.querySelectorAll('.filter-btn') as NodeListOf<HTMLButtonElement>;
        this.bulkActions = document.getElementById('bulkActions') as HTMLElement;
        this.clearCompletedBtn = document.getElementById('clearCompleted') as HTMLButtonElement;
        this.markAllCompleteBtn = document.getElementById('markAllComplete') as HTMLButtonElement;
        this.editModal = document.getElementById('editModal') as HTMLElement;
        this.editInput = document.getElementById('editInput') as HTMLInputElement;
        this.saveEditBtn = document.getElementById('saveEdit') as HTMLButtonElement;
        this.cancelEditBtn = document.getElementById('cancelEdit') as HTMLButtonElement;
        this.closeModalBtn = document.getElementById('closeModal') as HTMLButtonElement;
        
        this.countElements = {
            all: document.getElementById('countAll') as HTMLElement,
            active: document.getElementById('countActive') as HTMLElement,
            completed: document.getElementById('countCompleted') as HTMLElement
        };
    }

    /**
     * 绑定事件监听器
     */
    private bindEvents(): void {
        // 添加任务事件
        this.addBtn.addEventListener('click', () => this.addTodo());
        this.todoInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTodo();
            }
        });

        // 筛选按钮事件
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.getAttribute('data-filter') as FilterType;
                this.setFilter(filter);
            });
        });

        // 批量操作事件
        this.clearCompletedBtn.addEventListener('click', () => this.clearCompleted());
        this.markAllCompleteBtn.addEventListener('click', () => this.markAllComplete());

        // 编辑模态框事件
        this.saveEditBtn.addEventListener('click', () => this.saveEdit());
        this.cancelEditBtn.addEventListener('click', () => this.closeEditModal());
        this.closeModalBtn.addEventListener('click', () => this.closeEditModal());
        this.editInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.saveEdit();
            } else if (e.key === 'Escape') {
                this.closeEditModal();
            }
        });

        // 点击模态框背景关闭
        this.editModal.addEventListener('click', (e) => {
            if (e.target === this.editModal) {
                this.closeEditModal();
            }
        });
    }

    /**
     * 生成唯一 ID
     */
    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 添加新的 Todo 项目
     */
    private addTodo(): void {
        const text = this.todoInput.value.trim();
        if (!text) {
            this.todoInput.focus();
            return;
        }

        const newTodo: Todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        this.todos.unshift(newTodo); // 新任务添加到顶部
        this.todoInput.value = '';
        this.saveTodos();
        this.render();
        this.todoInput.focus();
    }

    /**
     * 切换 Todo 项目的完成状态
     */
    private toggleTodo(id: string): void {
        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            todo.completed = !todo.completed;
            todo.updatedAt = new Date();
            this.saveTodos();
            this.render();
        }
    }

    /**
     * 删除 Todo 项目
     */
    private deleteTodo(id: string): void {
        if (confirm('确定要删除这个任务吗？')) {
            this.todos = this.todos.filter(t => t.id !== id);
            this.saveTodos();
            this.render();
        }
    }

    /**
     * 开始编辑 Todo 项目
     */
    private startEdit(id: string): void {
        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            this.editingTodoId = id;
            this.editInput.value = todo.text;
            this.editModal.classList.add('show');
            this.editInput.focus();
            this.editInput.select();
        }
    }

    /**
     * 保存编辑
     */
    private saveEdit(): void {
        if (!this.editingTodoId) return;

        const newText = this.editInput.value.trim();
        if (!newText) {
            alert('任务内容不能为空！');
            this.editInput.focus();
            return;
        }

        const todo = this.todos.find(t => t.id === this.editingTodoId);
        if (todo) {
            todo.text = newText;
            todo.updatedAt = new Date();
            this.saveTodos();
            this.render();
        }

        this.closeEditModal();
    }

    /**
     * 关闭编辑模态框
     */
    private closeEditModal(): void {
        this.editModal.classList.remove('show');
        this.editingTodoId = null;
        this.editInput.value = '';
    }

    /**
     * 设置筛选器
     */
    private setFilter(filter: FilterType): void {
        this.currentFilter = filter;
        
        // 更新筛选按钮状态
        this.filterButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-filter') === filter) {
                btn.classList.add('active');
            }
        });

        this.render();
    }

    /**
     * 获取筛选后的 Todo 列表
     */
    private getFilteredTodos(): Todo[] {
        switch (this.currentFilter) {
            case 'active':
                return this.todos.filter(todo => !todo.completed);
            case 'completed':
                return this.todos.filter(todo => todo.completed);
            default:
                return this.todos;
        }
    }

    /**
     * 清除已完成的任务
     */
    private clearCompleted(): void {
        const completedCount = this.todos.filter(t => t.completed).length;
        if (completedCount === 0) {
            alert('没有已完成的任务需要清除！');
            return;
        }

        if (confirm(`确定要清除 ${completedCount} 个已完成的任务吗？`)) {
            this.todos = this.todos.filter(t => !t.completed);
            this.saveTodos();
            this.render();
        }
    }

    /**
     * 标记所有任务为完成
     */
    private markAllComplete(): void {
        const activeCount = this.todos.filter(t => !t.completed).length;
        if (activeCount === 0) {
            alert('所有任务都已完成！');
            return;
        }

        if (confirm(`确定要将 ${activeCount} 个任务标记为完成吗？`)) {
            this.todos.forEach(todo => {
                if (!todo.completed) {
                    todo.completed = true;
                    todo.updatedAt = new Date();
                }
            });
            this.saveTodos();
            this.render();
        }
    }

    /**
     * 更新计数显示
     */
    private updateCounts(): void {
        const allCount = this.todos.length;
        const activeCount = this.todos.filter(t => !t.completed).length;
        const completedCount = this.todos.filter(t => t.completed).length;

        this.countElements.all.textContent = allCount.toString();
        this.countElements.active.textContent = activeCount.toString();
        this.countElements.completed.textContent = completedCount.toString();

        // 显示/隐藏批量操作按钮
        if (allCount > 0) {
            this.bulkActions.style.display = 'flex';
        } else {
            this.bulkActions.style.display = 'none';
        }
    }

    /**
     * 创建 Todo 项目的 HTML 元素
     */
    private createTodoElement(todo: Todo): HTMLLIElement {
        const li = document.createElement('li');
        li.className = `todo-item ${todo.completed ? 'completed' : ''}`;
        li.setAttribute('data-id', todo.id);

        li.innerHTML = `
            <div class="todo-checkbox ${todo.completed ? 'checked' : ''}" data-action="toggle"></div>
            <span class="todo-text ${todo.completed ? 'completed' : ''}">${this.escapeHtml(todo.text)}</span>
            <div class="todo-actions">
                <button class="action-btn edit-btn" data-action="edit">编辑</button>
                <button class="action-btn delete-btn" data-action="delete">删除</button>
            </div>
        `;

        // 绑定事件
        li.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const action = target.getAttribute('data-action');

            switch (action) {
                case 'toggle':
                    this.toggleTodo(todo.id);
                    break;
                case 'edit':
                    this.startEdit(todo.id);
                    break;
                case 'delete':
                    this.deleteTodo(todo.id);
                    break;
            }
        });

        return li;
    }

    /**
     * 转义 HTML 特殊字符
     */
    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 渲染 Todo 列表
     */
    private render(): void {
        const filteredTodos = this.getFilteredTodos();

        // 清空列表
        this.todoList.innerHTML = '';

        // 显示/隐藏空状态
        if (filteredTodos.length === 0) {
            this.emptyState.style.display = 'block';
            this.todoList.style.display = 'none';
        } else {
            this.emptyState.style.display = 'none';
            this.todoList.style.display = 'block';

            // 渲染每个 Todo 项目
            filteredTodos.forEach(todo => {
                const todoElement = this.createTodoElement(todo);
                this.todoList.appendChild(todoElement);
            });
        }

        // 更新计数
        this.updateCounts();
    }

    // ==================== 本地存储功能 ====================

    /**
     * 保存 Todo 列表到本地存储
     */
    private saveTodos(): void {
        try {
            const todosJson = JSON.stringify(this.todos.map(todo => ({
                ...todo,
                createdAt: todo.createdAt.toISOString(),
                updatedAt: todo.updatedAt.toISOString()
            })));
            localStorage.setItem('todos', todosJson);
        } catch (error) {
            console.error('保存数据失败:', error);
            alert('保存数据失败，请检查浏览器存储空间！');
        }
    }

    /**
     * 从本地存储加载 Todo 列表
     */
    private loadTodos(): void {
        try {
            const todosJson = localStorage.getItem('todos');
            if (todosJson) {
                const todosData = JSON.parse(todosJson);
                this.todos = todosData.map((todo: any) => ({
                    ...todo,
                    createdAt: new Date(todo.createdAt),
                    updatedAt: new Date(todo.updatedAt)
                }));
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            this.todos = [];
            alert('加载数据失败，将使用空列表开始！');
        }
    }
}

// ==================== 应用程序初始化 ====================

/**
 * 当 DOM 加载完成后初始化应用程序
 */
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
    console.log('Todo List 应用程序已启动！');
});
